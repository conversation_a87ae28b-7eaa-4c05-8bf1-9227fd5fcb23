
import React, { useState, useEffect, useRef, useMemo, forwardRef, useImperativeHandle, } from "react";
import { Link, useNavigate } from "react-router-dom";
import Modal from "react-modal";
import { useLocation } from "react-router-dom";
import "./leftnav.css";
import { toast } from "react-toastify";
import Cookies from "universal-cookie";
import <PERSON>ropper from "react-easy-crop";
import Slider from "@material-ui/core/Slider";
import { IoLocationOutline } from "react-icons/io5";
import { MdDelete } from "react-icons/md";

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPhoneAlt } from '@fortawesome/free-solid-svg-icons';
import { Tooltip as ReactTooltip } from "react-tooltip";
import { TailSpin } from "react-loader-spinner";
import ChatBotComponent from "./ChatBotComponent";
import { motion } from 'framer-motion'
import { ThreeDots } from "react-loader-spinner";
import { MdCancel } from "react-icons/md";
import { saveAs } from 'file-saver';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import { useSelector } from 'react-redux';
import { useDispatch } from "react-redux";
import moment from 'moment';
import ScheduleMeet from "../Components/schedulemeet";
import { getDashboardData } from "../Views/utilities";
import chatbotMain from "./Chatbotmain";
import { fetchMeetings } from "../Views/utilities";
import { clearCandidates } from "../store/slices/candidateSlice";
import 'react-big-calendar/lib/css/react-big-calendar.css';
import ResumeUpload from "../Components/ResumeUpload";
//   Side icon for dashboard and all
// this for chat bot
// import { useEffect, useRef, useState } from "react";
import ChatbotIcon from "./chatbotIcon";
import ChatForm from "./chatbotForm";
import ChatMessage from "./chatMessage";
import { IoChatboxEllipses } from "react-icons/io5";
import { companyInfo } from "./companyinfo";
import { IoIosArrowDown } from "react-icons/io";
import { IoClose } from "react-icons/io5";
import "./chatbot.css"


const cookies = new Cookies();
Modal.setAppElement("#root");

const LeftNav = forwardRef(({ }, ref) => {
  const [chatMsgId, setChatMsgId] = useState(0);
  const localizer = momentLocalizer(moment);
  const [isOpen, setIsOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMessage, setModalMessage] = useState('');
  const [responseSuccess, setResponseSuccess] = useState(false);
  const [waitForSubmission1, setwaitForSubmission1] = useState(false);
  const [isActive, setIsActive] = useState(false);
  useImperativeHandle(ref, () => ({
    open: () => {
      // console.log('Opening modal');
      setIsOpen(true);

    },
    close: () => {
      // console.log('Closing modal');
      setIsOpen(false);
    },

  }));

  const [showImageModal, setShowImageModal] = useState(false);  // State to handle modal visibility


  const handleImageClicks = () => {
    setShowImageModal(true);
  };

  const closeModals = () => {
    setShowImageModal(false);  // Close modal
  };



  const { events } = useSelector((state) => state.meetingSliceReducer);
  // console.log(events, 'alldatas')
  // const recruiterEmails = events.map(event => event.recruiter_email);
  // console.log(recruiterEmails, 'recruiterEmails');

  const formats = {
    timeGutterFormat: 'HH:mm',
    eventTimeRangeFormat: ({ start, end }, culture, localizer) =>
      `${localizer.format(start, 'HH:mm', culture)} - ${localizer.format(end, 'HH:mm', culture)}`,
    agendaTimeRangeFormat: ({ start, end }, culture, localizer) =>
      `${localizer.format(start, 'HH:mm', culture)} - ${localizer.format(end, 'HH:mm', culture)}`,
  };
  const { managers } = useSelector((state) => state.userSliceReducer);
  const dropdownRef = useRef(null);
  if (Array.isArray(managers)) {
    const emails = managers.map(manager => manager.email);
    // console.log(emails, "emails");
  } else {
    console.log("Managers is not an array or is empty");
  }
  const { recruiters } = useSelector((state) => state.userSliceReducer);
  if (Array.isArray(recruiters)) {
    const recruiteremails = recruiters.map(recruiters => recruiters.email);
    // console.log(recruiteremails, "recruiteremails");
  } else {
    console.log("recruiters is not an array or is empty");
  }


  const { dashboardData } = useSelector((state) => state.dashboardSliceReducer);
  const dispatch = useDispatch();
  const candidatesdata = dashboardData.candidates || [];

  const emails = candidatesdata.map(candidate => candidate.email).filter(email => email);
  const uniqueDataEmail = Array.from(new Set(emails));

  // console.log(candidates, "candidates");
  // console.log(dashboardData, "dashb");
  const [selectedEmails, setSelectedEmails] = useState([]);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const [isDropdownOpen1, setIsDropdownOpen1] = useState(false);
  const [selectedEmails1, setSelectedEmails1] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const inputRef = useRef(null);
  const [selectedEvent, setSelectedEvent] = useState({
    title: '',
    attendees: '',
    cc_recipients: '',
    description: '',
    start_date: '',
    end_date: '',
    time_zone: '',
    start_time: '',
    end_time: '',
    rec_email: '',
  });
  //  console.log(selectedEvent, "selectedevent");
  // const loggedInEmail = localStorage.getItem('email').toLowerCase();
  // const handleEmailChange = (email) => {
  //   setSelectedEmails1(prev => {
  //     const newSelection = prev.includes(email)
  //       ? prev.filter(e => e !== email)
  //       : [...prev, email];

  //     // Ensure the state update is reflected in the `selectedEvent` as well
  //     setSelectedEvent(prevEvent => ({
  //       ...prevEvent,
  //       attendees: newSelection
  //     }));

  //     return newSelection;
  //   });
  // };
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.(com|in|net|org|co\.uk)$/;
  const isValidEmail = (email) => {
    return emailRegex.test(email);
  };

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
    setIsDropdownOpen1(true);
    setIsTyping(true);
  };

  // const handleFocus = () => {
  //   if (isTyping) {
  //     setIsDropdownOpen1(true);
  //   }
  // };

  // const handleBlur = () => {
  //   setTimeout(() => {
  //     if (inputRef.current && !inputRef.current.contains(document.activeElement)) {
  //       setIsDropdownOpen1(false);
  //     }
  //     setIsTyping(false);
  //   }, 100);
  // };

  // const handleAddNewEmail = (e) => {
  //   const newEmail = searchQuery.trim();
  //   if (newEmail && !selectedEmails1.includes(newEmail)) {
  //     handleCheckboxChange(newEmail);
  //     setSearchQuery('');
  //     setIsDropdownOpen1(false);
  //   }
  // };
  const handleAddNewEmail = () => {
    const newEmail = searchQuery.trim();

    if (isValidEmail(newEmail)) {
      if (newEmail && !selectedEmails1.includes(newEmail)) {
        handleCheckboxChange(newEmail);
        setSelectedEmails1([...selectedEmails1,newEmail])
        setSearchQuery('');
        setIsDropdownOpen1(false);
      }
    } else {
      toast.error('Enter a valid email address.');
    }
  };




  const filteredEmails = uniqueDataEmail.filter(email =>
    email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  useEffect(() => {
    if (selectedEvent?.attendees) {
      let attendeesList = [];
      if (typeof selectedEvent.attendees === 'string') {
        attendeesList = selectedEvent.attendees.split(',').map(email => email.trim()).filter(email => email);
      } else if (Array.isArray(selectedEvent.attendees)) {
        attendeesList = selectedEvent.attendees.filter(email => email);
      }
      setSelectedEmails1(attendeesList);
    } else {
      setSelectedEmails1([]);
    }
  }, [selectedEvent]);



  const handleCheckboxChange = (email) => {
    setSelectedEmails1(prevSelectedEmails => {
      const newSelectedEmails = prevSelectedEmails.includes(email)
        ? prevSelectedEmails.filter(item => item !== email)
        : [...prevSelectedEmails, email];
      return newSelectedEmails;
    });
  };

  //PAVAN


  const [searchQuerys, setSearchQuerys] = useState(null);


  const toggleDropdown = () => {
    if (filteredManagers.length > 0) {
      setIsDropdownOpen(prev => !prev);
    }
  };


  const handleInputChange = (e) => {
    setSearchQuerys(e.target.value);
    if (!isDropdownOpen) {
      setIsDropdownOpen(true);
    }
  };

  const handleEmailChange1 = (email) => {
    setSelectedEmails(prevSelectedEmails => {
      const newSelectedEmails = prevSelectedEmails.includes(email)
        ? prevSelectedEmails.filter(item => item !== email)
        : [...prevSelectedEmails, email];
      setSearchQuerys('');
      return newSelectedEmails;
    });
  };

  const handleAddManualEmail = () => {
    if (searchQuerys && !selectedEmails.includes(searchQuerys)) {
      setSelectedEmails([...selectedEmails, searchQuerys]);
      setSearchQuerys('');
    }
  };



  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const loggedInEmail = localStorage.getItem('email').toLowerCase();

  // Filter managers and recruiters, excluding the logged-in user's email
  const filteredManagers = managers.filter(manager =>
    manager.email.toLowerCase().includes(searchQuerys?.toLowerCase()) &&
    manager.email.toLowerCase() !== loggedInEmail
  );

  const filteredRecruiters = recruiters.filter(recruiter =>
    recruiter.email.toLowerCase().includes(searchQuerys?.toLowerCase()) &&
    recruiter.email.toLowerCase() !== loggedInEmail
  );
  const selectedEmailsDisplay = selectedEmails.join(', ');
  const inputValues = selectedEmailsDisplay || searchQuerys;


  // useEffect(() => {
  //   if (onEmailChange) {
  //     onEmailChange(selectedEmails);
  //   }
  // }, [selectedEmails, onEmailChange]);

  const handleCloseDropdown = (e) => {
    // Close the dropdown if clicked outside
    if (!e.target.closest('.dropdown')) {
      setIsDropdownOpen(false);
    }
  };

  // Add event listener to close dropdown when clicking outside
  React.useEffect(() => {
    document.addEventListener('click', handleCloseDropdown);
    return () => {
      document.removeEventListener('click', handleCloseDropdown);
    };
  }, []);
  const [EditModal, setEditModal] = useState(false);
  const EditopenModal = () => {
    setEditModal(true);
    setShowCalendar(false);
    setIsOpen(false);
    setShowmeet(false);
    //  setSelectedEvent(event);
  setIsInitialized(false); // 👈 Reset on modal open
  setFiles([]);   
  }
  const EditcloseModal = () => {
    setEditModal(false);
    setSearchQuerys('');
    setFiles([])
    setSearchQuery('');
    setIsDropdownOpen1(false);
    setIsDropdownOpen(false);
      setIsInitialized(false);
     fetchMeetings()

  }
  const msgs = useMemo(() => {
    return ["Welcome to Makonis", "I am Jimmy", "How can i help you?"];
  }, []);
  const [i, setI] = useState(1);
  useEffect(() => {
    if (chatMsgId !== -1) {
      const intervalId = setInterval(() => {
        setChatMsgId(chatMsgId + 1);
      }, 2000)
      return () => clearInterval(intervalId);
    } else {
      if (chatMsgId === 3) {
        setChatMsgId(-1);
      }
    }
  }, [chatMsgId])

  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');
  const timeOptions = Array.from({ length: 24 * 2 }, (_, i) => {
    const hour = Math.floor(i / 2);
    const minute = (i % 2) * 30;
    return {
      value: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
      label: `${hour}:${minute.toString().padStart(2, '0')}`
    };
  });
  const handleStartTimeChange = (e) => {
    const newStartTime = e.target.value;
    setSelectedEvent(prevState => {
      // Calculate end time 30 minutes after the new start time
      const [hours, minutes] = newStartTime.split(':').map(Number);
      const startDate = new Date();
      startDate.setHours(hours);
      startDate.setMinutes(minutes);

      const endDate = new Date(startDate.getTime() + 30 * 60000); // Add 30 minutes

      const endHours = endDate.getHours().toString().padStart(2, '0');
      const endMinutes = endDate.getMinutes().toString().padStart(2, '0');
      const newEndTime = `${endHours}:${endMinutes}`;

      return {
        ...prevState,
        start_time: newStartTime,
        end_time: newEndTime,
      };
    });
  };
  const handleEndTimeChange = (e) => {
    const newEndTime = e.target.value;
    setSelectedEvent(prevState => ({
      ...prevState,
      end_time: newEndTime,
    }));
  };


  const add30Minutes = (time) => {
    const [hours, minutes] = time.split(':').map(Number);
    let newMinutes = minutes + 30;
    let newHours = hours;

    if (newMinutes >= 60) {
      newMinutes -= 60;
      newHours += 1;
    }

    if (newHours === 24) {
      newHours = 0;
    }

    return `${String(newHours).padStart(2, '0')}:${String(newMinutes).padStart(2, '0')}`;
  };

  useEffect(() => {
    if (startTime) {
      setEndTime(add30Minutes(startTime));
    } else {
      setEndTime('');
    }
  }, [startTime]);
  // console.log(meeting_id, 'Deleting meeurlng with ID');
  // const [waitForSubmission3, setwaitForSubmission3] = useState(false);
  const [waitForSubmissiondel, setwaitForSubmissiondel] = useState(false);
  const handleDeletemeet = async (meeting_id) => {
    if (!waitForSubmissiondel) {
      setwaitForSubmissiondel(true);

      try {
        const response = await fetch(
          `http://************:5002/delete_event`, // Your endpoint
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              meeting_id: meeting_id, // Send the meeting_id from Redux
            }),
          },
        );

        // Check if the response is OK
        if (response.ok) {
          setShowmeet(false)
          setShowCalendar(false)
          setIsOpen(false);
          const data = await response.json();

          //       // Show a success message
          setwaitForSubmissiondel(false);
          toast.success("Meeting deleted successfully!");
          fetchMeetings();
          // Update Redux state or UI here if needed



        } else {
          // Handle HTTP error responses
          toast.error("Failed to delete meeting. Please try again.");
          setwaitForSubmissiondel(false);
        }

      } catch (err) {
        console.error(err);
        setwaitForSubmissiondel(false);
        toast.error("Error occurred, Please try again.");
      }
    }
  };


  const handleDownloadQuestions = () => {
    if (interviewData && interviewData.response && interviewData.response[0]) {
      const heading = interviewData.response[0].heading;
      let questions = [];

      if (general === "select all") {
        Object.keys(interviewData.response[0].questions).forEach(category => {
          questions.push(`<div style="font-weight: bold; color: black;">${category}</div>`);
          questions = questions.concat(interviewData.response[0].questions[category].map(cleanQuestion));
          questions.push("<br/>");
        });
      } else {
        questions.push(`<div style="font-weight: bold; color: green;">${general}</div>`);
        questions = questions.concat(interviewData.response[0].questions[general].map(cleanQuestion));
      }
      const content = `<html><body><div><span style="font-weight: bold; color: green;">${heading}</span></div><div>${questions.join('<br/>')}</div></body></html>`;
      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
      saveAs(blob, 'interview_questions.doc');
    }
  };


  const [notificationCount, setNoficationCount] = useState(0);
  const { pathname } = useLocation();
  const initial = {
    dashboard: false,
    registercandidate: false,
    joblisting: false,
    assignedrequirements: false,
  };
  const [show, setShow] = useState(initial);
  const [highlight, setHighlight] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [Telephonic, setTelephonic] = useState(false);
  const navigate = useNavigate();
  const USERTYPE = cookies.get("USERTYPE");
  const [userName, setUserName] = useState(cookies.get("USERNAME"));
  const [profileImage, setProfileImage] = useState(
    localStorage.getItem("profileImage") ||
    "https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_1280.png",
  );

  const [showOptions, setShowOptions] = useState(false);
  const [isCropping, setIsCropping] = useState(false);
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);
  const [imageForCropping, setImageForCropping] = useState(null);
  const [isPopupVisible, setIsPopupVisible] = useState(false);
  const timeoutDuration = 9000;
  const handleUserActivity = () => {
    if (!isPopupVisible) setI(1);
  };

  useEffect(() => {
    getCountOfNotifications();
    setToInitial()
  }, []);

  useEffect(() => {
    localStorage.getItem("profileImage");
  }, []);


  useEffect(() => {
    let lastActivityTime = Date.now();

    const checkTimeout = () => {
      const currentTime = Date.now();
      const elapsedTime = currentTime - lastActivityTime;

      if (elapsedTime > timeoutDuration * 1000) {
        setIsPopupVisible(true);
        handleConfirmLogout(2);
      }
    };

    const interval = setInterval(() => {
      checkTimeout();
    }, 1000);

    const handleUserActivity = () => {
      lastActivityTime = Date.now();
      // Additional logic if needed
    };

    window.addEventListener("mousemove", handleUserActivity);
    window.addEventListener("keypress", handleUserActivity);
    window.addEventListener("click", handleUserActivity);
    window.addEventListener("scroll", handleUserActivity);

    // Cleanup function to clear the interval and event listeners
    return () => {
      clearInterval(interval);
      window.removeEventListener("mousemove", handleUserActivity);
      window.removeEventListener("keypress", handleUserActivity);
      window.removeEventListener("click", handleUserActivity);
      window.removeEventListener("scroll", handleUserActivity);
    };
  }, []);
  const optionsRef = useRef(null);
  const imageContainerRef = useRef(null);

  useEffect(() => {
    setUserName(cookies.get("USERNAME"));
    // if (!profileImage) {
    fetchUserProfileImage();
    // }
  }, [localStorage.getItem("user_id")]);

  const fetchUserProfileImage = async () => {
    try {
      const response = await fetch(
        `http://************:5002/user_image/${localStorage.getItem("user_id")}`,
        {
          method: "GET",
        },
      );

      if (!response.ok) {
        setProfileImage(
          "https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_1280.png",
        );
      } else {
        // console.log("Response : ", response.statusText);
        const blob = await response.blob();
        const imageUrl = await new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result);
          reader.onerror = () => reject(reader.error);
          reader.readAsDataURL(blob);
        });
        setProfileImage(imageUrl);
        // console.log("Image URL :",decodeURIComponent(imageUrl))
        localStorage.setItem("profileImage", imageUrl);
      }
    } catch (error) {
      console.error("Error fetching image:", error);
      localStorage.removeItem("profileImage");
      setProfileImage(
        "https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_1280.png",
      );
    }
  };

  // useEffect(()=>{
  //   setNoficationCount(localStorage.getItem('num_notification'))
  // },[])
  useEffect(() => {
    // console.log(notificationCount);
  }, [notificationCount]);

  const handleLogout = () => {
    setToInitial();
    setShowModal(true);
  };
  const [showCalendar, setShowCalendar] = useState(false);
  const handleCalendar = () => {

    fetchMeetings()
    setShowCalendar(true);
  };
  const closeModal = () => {
    setShowCalendar(false);
    setIsOpen(false)
  };

  const [showmeet, setShowmeet] = useState(false);

  const handlemeet = () => {
    setShowmeet(true);
  };
  const closemeet = () => {
    setShowmeet(false);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setTelephonic(false);
    setInterviewData(null);
    setgeneral("");

  };
  const handleOpenModal = () => {
    setTelephonic(true);
  };

  // const email = localStorage.getItem('email');
  // console.log(email);

  const handleConfirmLogout = async (identify) => {
    try {
      const response = await fetch("http://************:5002/logout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          user_id: localStorage.getItem("user_id"),
        }),
      });

      if (response.ok) {
        dispatch(clearCandidates());
        const responselogout = await response.json();

        // console.log("logout res", responselogout);
        cookies.remove("USERNAME", { path: "/" });
        cookies.remove("USERTYPE", { path: "/" });
        localStorage.removeItem("profileImage");
        localStorage.removeItem("selectedProfile");
        localStorage.removeItem("selectedSkills");
        window.location.reload();
        if (localStorage.getItem("user_type")) {
          setToInitial();
          if (localStorage.getItem("user_type") === "management") {
            if (identify === 1) {
              navigate("/ManagementLogin");
            } else {
              navigate("/ManagementLogin", { state: { isPopupvisible: true } });
            }
          } else {
            if (identify === 1) {
              navigate("/RecruitmentLogin");
            } else {
              navigate("/RecruitmentLogin", {
                state: { isPopupvisible: true },
              });
            }
          }
        }
          setTimeout(() => {
        window.location.reload();
      }, 100);
      } else {
        console.error("Logout failed:", response.statusText);
      }
    } catch (error) {
      console.error("Error during logout:", error);
    }
  };

  const handleImageClick = () => {
    setShowOptions(!showOptions);
  };

  const handleUploadClick = (event) => {
    const fileInput = document.getElementById("fileInput");
    fileInput.addEventListener("change", handleFileChange);
    fileInput.click();
  };

  const handleFileChange = async (event) => {
    const file = event.target.files[0];

    if (file) {
      try {
        const imageUrl = URL.createObjectURL(file);
        setImageForCropping(imageUrl);
        setIsCropping(true);
      } catch (error) {
        console.error("Error reading file:", error);
      }
    }
  };

  const handleRemovePhoto = async () => {
    setShowOptions(false);

    try {
      const response = await fetch(
        `http://************:5002/delete_user_image/${localStorage.getItem("user_id")}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            // Send the profile image URL to the server for deletion
            profileImage: localStorage.getItem("profileImage"),
            image_delete_status: true,
          }),
        },
      );

      if (!response.ok) {
        throw new Error("Failed to remove image");
      }

      const result = await response.json();
      if (localStorage.getItem("profileImage")) {
        // Remove the profile image URL from local storage
        localStorage.removeItem("profileImage");
      }

      // Remove the profile image URL from local storage
      fetchUserProfileImage();
      // console.log("Image rem/oved successfully");
      // Set the profile image state to null to update the UI
      setProfileImage(null);
    } catch (error) {
      console.error("Error removing image:", error);
    }
  };

  const handleClickOutside = (event) => {
    if (
      optionsRef.current &&
      !optionsRef.current.contains(event.target) &&
      imageContainerRef.current &&
      !imageContainerRef.current.contains(event.target)
    ) {
      setShowOptions(false);
    }
  };

  useEffect(() => {
    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, [showOptions]);

  useEffect(() => {
    setShow(initial);
  }, []);

  useEffect(() => {
    // console.log('useeffect')
    // console.log(localStorage.getItem('path'))
    if (localStorage.getItem("path")) {
      const parent = localStorage
        .getItem("path")
        ?.trim()
        .substring(1)
        .toLowerCase();
      // console.log(parent);
      setShow({
        ...initial,
        [parent]: true,
      });
      // console.log({
      //   ...initial,
      //   [parent]: true,
      // });
    } else {
      console.log("else case");
    }
  }, [localStorage.getItem("path")]);

  useEffect(() => {
    console.log(show);
  }, []);
  const capitalizeFirstLetter = (string) => {
    return string.charAt(0).toUpperCase() + string.slice(1);
  };
  // @app.route('/checked_jobs_notification/<int:user_id>', methods=['POST'])
  const getCountOfNotifications = async () => {
    // console.log('getcountofnotification called')
    // console.log("in getCountOfNotifications function");
    // @app.route('/jobs_notification/<int:user_id>', methods=['GET'])
    const id = localStorage.getItem("user_id");
    try {
      const response = await fetch(
        `http://************:5002/jobs_notification/${id}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        },
      );
      // console.log('reached here')
      if (response.ok) {
        const data = await response.json();
        // console.log(data);
        // localStorage.setItem('num_notification',data[0].num_notification)
        let sum = 0;
        for (let j = 0; j < data?.length; j++) {
          sum += data[j].num_notification;
        }
        setNoficationCount(sum);
      } else {
        console.log(response.statusText);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const updateNotificationCount = async () => {
    const id = localStorage.getItem("user_id");
    try {
      const response = await fetch(
        `http://************:5002/checked_jobs_notification/${id}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            checked_notification_status: true,
          }),
        },
      );
      if (response.ok) {
        // console.log("in ok");
        const data = response.json();
        await getCountOfNotifications();
        // console.log(data);
      } else {
        // console.log(response.statusText);
      }
    } catch (err) {
      console.log("handle error", err);
    }
  };


  const assignRequirementClicked = async () => {
    // localStorage.setItem('num_notification')
    setToInitial();

    await updateNotificationCount();
  };

  const userNameCapitalized = userName ? capitalizeFirstLetter(userName) : "";

  const createImage = (url) =>
    new Promise((resolve, reject) => {
      const image = new Image();
      image.addEventListener("load", () => resolve(image));
      image.addEventListener("error", (error) => reject(error));
      image.setAttribute("crossOrigin", "anonymous");
      image.src = url;
    });

  const getCroppedImg = async (imageSrc, pixelCrop) => {
    const image = await createImage(imageSrc);
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");

    canvas.width = pixelCrop.width;
    canvas.height = pixelCrop.height;

    ctx.drawImage(
      image,
      pixelCrop.x,
      pixelCrop.y,
      pixelCrop.width,
      pixelCrop.height,
      0,
      0,
      pixelCrop.width,
      pixelCrop.height,
    );

    return new Promise((resolve, reject) => {
      canvas.toBlob((blob) => {
        if (!blob) {
          reject(new Error("Canvas is empty"));
          return;
        }
        blob.name = "croppedImage.jpeg";
        resolve(blob);
      }, "image/jpeg");
    });
  };

  const handleSaveCroppedImage = async () => {
    try {
      const croppedImageBlob = await getCroppedImg(
        imageForCropping,
        croppedAreaPixels,
      );
      // console.log("Cropped Image Blob:", croppedImageBlob);
      const formData = new FormData();
      formData.append("image", croppedImageBlob, "croppedImage.jpeg");

      const reader = new FileReader();
      reader.readAsDataURL(croppedImageBlob);
      reader.onloadend = async () => {
        const base64String = reader.result.replace(
          /^data:image\/(png|jpeg);base64,/,
          "",
        );
        await uploadCroppedImage(base64String, "croppedImage.jpeg");
      };
    } catch (error) {
      console.error("Error saving cropped image:", error.message);
    }
  };

  const uploadCroppedImage = async (base64Image, filename) => {
    try {
      // console.log("Base64 Image String:", typeof base64Image);

      const response = await fetch(
        `http://************:5002/upload_user_image/${localStorage.getItem("user_id")}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            image: base64Image,
            filename,
            image_delete_status: false,
          }),
        },
      );

      if (!response.ok) {
        throw new Error("Failed to upload image");
      }

      // console.log("Response Status Text:", response.statusText);

      const result = await response.json();
      // console.log("Upload Result:", result);
      fetchUserProfileImage();
      const newImageUrl = result.imageUrl; // Assuming the server returns the image URL
      setProfileImage(newImageUrl);
      localStorage.setItem("profileImage", newImageUrl);
    } catch (error) {
      console.error("Error uploading cropped image:", error);
    } finally {
      setIsCropping(false);
      setShowOptions(false);
    }
  };
  const fileToBase64 = (file) => {
    if (file === null) return;
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        resolve(reader.result.split(",")[1]);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
      // console.log(file, "pdf")
    });
  };
  const handleresumeChange = (e) => {
    setSelectedFile(e.target.files[0]);
  };
  const [interviewData, setInterviewData] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [selectedOption, setSelectedOption] = useState('');
  const [description, setDescription] = useState('');
  const handleDescriptionChange = (e) => {
    setDescription(e.target.value);
  };
  const [general, setgeneral] = useState("")

  //console.log(events,"inleftnav")

  const transformEvents = (events) => {
    return events?.map(event => {
      const { title, start_date, description, end_date, start_time, end_time, join_url, meeting_id, time_zone, cc_recipients, attendees, rec_email, files } = event;

      // Combine date and time into Date objects
      const startDateTime = new Date(`${start_date}T${start_time}`);
      const endDateTime = new Date(`${end_date}T${end_time}`);
      return {
        title,
        start: startDateTime,
        end: endDateTime,
        description,
        join_url,
        meeting_id,
        start_time,
        end_time,
        attendees,
        cc_recipients,
        time_zone,
        rec_email,
        files
      };
    });
  };

  // console.log(transformEvents(events), "transformedEvent");
  const transformedEvents = transformEvents(events);
  // console.log(transformedEvents, "transformedEvents");
  const cleanQuestion = (question) => {
    return question.replace(/[*#]+/g, '').trim();
  };
  const handleSelectChange = (e) => {
    setSelectedOption(e.target.value);
  };
  const handlegeneralchange = (e) => {
    setgeneral(e.target.value)
  }
  const [waitForSubmission, setWaitForSubmission] = useState(false);
  const [selectAllData, setSelectAllData] = useState([]);
  const [indexes, setIndexes] = useState([]);
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setWaitForSubmission(true)
      const base64String = await fileToBase64(selectedFile);
      // console.log("Base64 String:");
      const body_data = {
        user_id: localStorage.getItem("user_id"),
        recruiter_prompt: selectedOption,
        resume: base64String

      };
      // console.log("job post Request Body:", body_data);

      const response = await fetch(
        "http://************:5002/generate_questions", {
        // "api/generate_questions", {
        method: "POST",
        // mode: "no-cors",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body_data),
      });

      const data = await response.json();
      setWaitForSubmission(false);
      if (!data.response || !data.response[0] || data.response[0].questions === undefined || Object.keys(data.response[0].questions).length === 0) {
        setInnerModal(true);
      }
      // console.log(data.response[0]?.questions)
      let tempList = []
      let tempIndexes = []
      const selectAllKeys = Object.keys(data.response[0]?.questions);
      for (let i = 0; i < Object.keys(data.response[0].questions).length; i++) {
        tempIndexes.push(tempList.length);
        tempList.push(selectAllKeys[i])
        const tempArr = data.response[0].questions[selectAllKeys[i]]
        console.log(tempArr)
        tempList = tempList.concat(tempArr)
      }
      // console.log(tempList)
      setIndexes(tempIndexes)
      setSelectAllData(tempList)
      setInterviewData(data);
      // console.log("data", data);

    } catch (err) {
      // console.log("handle error", err);
    }

  };
  const [files, setFiles] = useState([]);
  const [existingFiles, setExistingFiles] = useState([]);
  //  useEffect(() => {
  //   if (events ) {
  //     const matchedEvent = events.find(event => event.meeting_id === selectedMeetingId);
  //     if (matchedEvent?.files) {
  //       setExistingFiles(matchedEvent.files);
  //     }
  //   }
  // }, [events, selectedMeetingId]);

  // Initialize existing files on load
  const [isInitialized, setIsInitialized] = useState(false);
useEffect(() => {
  if (selectedEvent && !isInitialized) {
    if (typeof selectedEvent.files === "string") {
      try {
        const parsed = JSON.parse(selectedEvent.files);
        setExistingFiles(parsed);
      } catch (e) {
        console.error("Failed to parse existing files", e);
      }
    } else if (Array.isArray(selectedEvent.files)) {
      setExistingFiles(selectedEvent.files);
    }
    setIsInitialized(true);
  }
}, [selectedEvent, isInitialized]);




  // console.log(selectedEvent.files, "existingFiles");
  // console.log(existingFiles, "existingFiles333333");

  const eventfileToBase64 = (file) => new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const result = reader.result;
      const base64 = result.split(',')[1];
      resolve(base64);
    };
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });

  const handleFileChangeevent = (e) => {
    const selected = Array.from(e.target.files);
    setFiles((prev) => [...prev, ...selected]);
  };

  // const handleRemoveFile = (indexToRemove) => {
  //   const updatedFiles = files.filter((_, index) => index !== indexToRemove);
  //   setFiles(updatedFiles);
  // };
  const handleRemoveFile = (index) => {
    setFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const handleRemoveExistingFile = (index) => {
    setExistingFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const handleFormSubmit = async (e) => {
    e.preventDefault(); // Prevent default form submission

    if (!selectedEvent.title || !startDateValue || !endDateValue || !selectedEvent.start_time || !selectedEvent.end_time || !selectedEvent.time_zone || selectedEmails1.length === 0) {
      toast.error('Please fill in all required fields.');
      return; // Prevent further execution
    }
    const filePayload = await Promise.all(
      files.map(async (file) => ({
        file_name: file.name,
        file_content_base64: await eventfileToBase64(file),
      }))
    );

    const payload = {
      subject: selectedEvent.title,
      attendees: selectedEmails1,
      cc_recipients: selectedEmails,
      description: selectedEvent.description,
      start_date: startDateValue,
      end_date: endDateValue,
      time_zone: selectedEvent.time_zone,
      start_time: selectedEvent.start_time,
      end_time: selectedEvent.end_time,
      recruiter_email: localStorage.getItem("email"),
      meeting_id: selectedEvent.meeting_id, // Add meeting_id
      rec_email: selectedEvent.email,
      existing_files: existingFiles.length > 0 ? existingFiles : null,
      new_files: filePayload,
    };
    if (!waitForSubmission1) {
      setwaitForSubmission1(true);
      setResponseSuccess(false);

      try {
        // Send the request
        const response = await fetch("http://************:5002/update_event", {
          method: 'POST', // Method should be 'POST' if you're creating or updating the resource
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        });

        if (!response.ok) {
          throw new Error('Network response was not ok');
          setwaitForSubmission1(false);
        }

        const result = await response.json();
        // console.log('Success:', result);
        setwaitForSubmission1(false);
        EditcloseModal();
        fetchMeetings();
        setModalMessage('Meeting edited successfully');
        setResponseSuccess(true);
        setIsModalOpen(true);
        // await syncEvents();
       fetchMeetings();
      } catch (error) {
        console.error('Error:', error);
        setwaitForSubmission1(false);
        setModalMessage('An error occurred. Please try again.');
        setIsModalOpen(true);
      }
    }
  };


  //   e.preventDefault();
  //   console.log("Form submitted with data:", {
  //     title: selectedEvent.title,
  //     attendees: selectedEmails1,
  //     cc_recipients: selectedEmails,
  //     start_date: startDateValue,
  //     end_date: endDateValue,
  //     time_zone: selectedEvent.time_zone,
  //     start_time: selectedEvent.start_time,
  //     end_time: selectedEvent.end_time
  //   });

  //   const eventData = {
  //     title: selectedEvent.title || '',
  //     attendees: selectedEmails1,
  //     cc_recipients: selectedEmails,
  //     start_date: startDateValue,
  //     end_date: endDateValue,
  //     time_zone: selectedEvent.time_zone || '',
  //     start_time: selectedEvent.start_time || '',
  //     end_time: selectedEvent.end_time || ''
  //   };

  //   try {
  //     const response = await fetch(`http://************:5002/update_event/${selectedEvent.id}`, {
  //       method: 'POST',
  //       headers: {
  //         'Content-Type': 'application/json',
  //       },
  //       body: JSON.stringify(eventData),
  //     });

  //     if (!response.ok) {
  //       throw new Error('Network response was not ok');
  //     }

  //     const result = await response.json();
  //     console.log('Success:', result);

  //     // Close modal on success
  //     setEditModalOpen(false);
  //   } catch (error) {
  //     console.error('Error:', error);
  //   }
  // };


  const handleCropComplete = (croppedArea, croppedAreaPixels) => {
    setCroppedAreaPixels(croppedAreaPixels);
  };
  const setToInitial = () => {
    // if (localStorage.getItem("page_no")) {
    //   localStorage.removeItem("page_no");
    // }
    localStorage.removeItem("path");
  };
  const [innerModal, setInnerModal] = useState(false);

  const handleCloseInnerModal = () => {
    setInnerModal(false);
  }
  const handleMouseEnter = () => {
    setShowOptions(true);
  };

  const handleMouseLeave = () => {
    setShowOptions(false);
  };
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const toggleSidebar = () => {
    // Check if the current window width is 542px or less
    if (window.innerWidth <= 542) {
      setSidebarOpen(!sidebarOpen);

      if (sidebarOpen) {
        document.querySelector("body").classList.remove("active");
        document.querySelector("body").classList.add("barside2");
      } else {
        document.querySelector("body").classList.add("active");
        document.querySelector("body").classList.add("closebar");
      }
    }
  };

  const handleEventClick = (event) => {
    // Here you can set the event data in the state to display in the modal
    setSelectedEvent(event);
    // console.log(event, "slecsf"); // Assuming you have state to hold the selected event
    setShowmeet(true); // This will open the handlemeet modal
  };
  // const closemeet = () => setShowMeet(false);
  useEffect(() => {
    if (typeof selectedEvent?.attendees === 'string') {
      const emails = selectedEvent.attendees.split(',').map(email => email.trim());
      setSelectedEmails1(emails);
    }
  }, [selectedEvent?.attendees]);

  useEffect(() => {
    if (typeof selectedEvent?.cc_recipients === 'string') {
      const emails = selectedEvent.cc_recipients.split(',').map(email => email.trim());
      setSelectedEmails(emails);
    }
  }, [selectedEvent?.cc_recipients]);

  const startDate = new Date(selectedEvent?.start);
  const endDate = new Date(selectedEvent?.end);

  const formattedStartDate = startDate.toLocaleDateString('en-US', {
    weekday: 'long', // Full day name
    month: 'long',   // Full month name
    day: 'numeric'   // Day of the month
  });

  const formattedStartTime = startDate.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false    // 24-hour format
  });

  const formattedEndTime = endDate.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false    // 24-hour format
  });

  const parseDate = (dateValue) => {
    const parsedDate = new Date(dateValue);
    return isNaN(parsedDate.getTime()) ? new Date() : parsedDate;
  };

  // Ensure default values in case selectedEvent or dates are undefined
  const [endDateManuallyChanged, setEndDateManuallyChanged] = useState(false);

  const startDates = parseDate(selectedEvent?.start);
  const endDates = parseDate(selectedEvent?.end);

  const startDateValue = startDates.toISOString().split('T')[0];
  const endDateValue = endDates.toISOString().split('T')[0];

  // const handleStartDateChange = (e) => {
  //   const newStartDate = new Date(e.target.value);
  //   if (!isNaN(newStartDate.getTime())) {
  //     setSelectedEvent({ ...selectedEvent, start: newStartDate });
  //   }
  // };
  const handleStartDateChange = (e) => {    // new functionality
  const newStartDate = new Date(e.target.value);
  if (!isNaN(newStartDate.getTime())) {
    setSelectedEvent((prev) => {
      // If user hasn't modified end date manually, sync end date with start date
      const newEndDate = endDateManuallyChanged ? prev.end : newStartDate;
      return {
        ...prev,
        start: newStartDate,
        end: newEndDate,
      };
    });
  }
};

  // const handleEndDateChange = (e) => {
  //   const newEndDate = new Date(e.target.value);
  //   if (!isNaN(newEndDate.getTime())) {
  //     setSelectedEvent({ ...selectedEvent, end: newEndDate });
  //   }
  //   if (new Date(startDate) > new Date(newEndDate)) {
  //     seterror('End Date is Earlier than Start Date .');
  //     toast.error('End Date is Earlier than Start Date.');
  //   } else {
  //     seterror(''); // Clear error if validation passes
  //   }
  // };

   const handleEndDateChange = (e) => {   // new functionality
  const newEndDate = new Date(e.target.value);
  if (!isNaN(newEndDate.getTime())) {
    setSelectedEvent((prev) => ({
      ...prev,
      end: newEndDate,
    }));
    setEndDateManuallyChanged(true); // Mark end date as manually changed
  }

  if (new Date(startDates) > newEndDate) {
    seterror('End Date is Earlier than Start Date.');
    toast.error('End Date is Earlier than Start Date.');
  } else {
    seterror('');
  }
};


  // const handleStartTimeChange = (e) => {
  //   const newStartTime = e.target.value;
  //   setSelectedEvent({ ...selectedEvent, start_time: newStartTime });
  // };

  // const handleEndTimeChange = (e) => {
  //   const newEndTime = e.target.value;
  //   setSelectedEvent({ ...selectedEvent, end_time: newEndTime });
  // };

  const [interviewModal, setInterviewModal] = useState(false);

  const InterviewcloseModal = () => {
    setInterviewModal(false);
    setShowCalendar(false);
    // resetForm();

  };

  const [start_autoDate, setStartautoDate] = useState("");
  const [end_autoDate, setEndautoDate] = useState("");
  const [startautoTime, setStartautoTime] = useState("");
  const [endautoTime, setEndautoTime] = useState("");

  const handleEmptySlotClick = (slotInfo) => {
    // Open the modal here


    setInterviewModal(true);
    setShowCalendar(false);

    // Optionally, set start and end times based on the slot clicked
    setStartautoDate(slotInfo.start.toLocaleDateString('en-CA'));
    setEndautoDate(slotInfo.end.toLocaleDateString('en-CA'));
    setStartautoTime(slotInfo.start.toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit' }));
    setEndautoTime(slotInfo.end.toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit' }));

  };
  // console.log(start_autoDate, "start_autoDate check in left nav page");
  const formatTimeTo24Hour = (timeString) => {
    const [time, modifier] = timeString.split(' ');
    let [hours, minutes] = time.split(':');

    if (modifier === 'PM' && hours !== '12') {
      hours = parseInt(hours, 10) + 12;
    }
    if (modifier === 'AM' && hours === '12') {
      hours = '00';
    }

    return `${hours.padStart(2, '0')}:${minutes}`;
  };
  useEffect(() => {
    if (selectedEvent && selectedEvent.start_time && selectedEvent.end_time) {
      const formattedStartTime = formatTimeTo24Hour(selectedEvent.start_time);
      const formattedEndTime = formatTimeTo24Hour(selectedEvent.end_time);
      setStartTime(formattedStartTime);
      setEndTime(formattedEndTime);
    }
  }, [selectedEvent]);
  const [error, seterror] = useState('');
    // const handleEndDateChange = (e) => {
    //   const newEndDate = e.target.value;
    //   setEndDate(newEndDate);
  
    //   if (new Date(startDate) > new Date(newEndDate)) {
    //     seterror('End Date is Earlier than Start Date .');
    //     toast.error('End Date is Earlier than Start Date.');
    //   } else {
    //     seterror(''); // Clear error if validation passes
    //   }
  
    // };


  const [ResumeModal, setResumeModal] = useState(false);

  const handleResumeUpload = () => {
    setResumeModal(true)
  }
  const handleCloseResume = () => {
    setResumeModal(false)
  }
  const [selectedOptions, setSelectedOptions] = useState("Job_Description");

  const [jdselectedFile, setjdSelectedFile] = useState(null);
  const handleJobdescriptionchange = (e) => {
    setjdSelectedFile(e.target.files[0]);
  };
  const [jdQuestions, setJdQuestions] = useState([]);
  const handleSubmitJD = async (e) => {
    e.preventDefault();
    const jdText = document.getElementById("jdText")?.value;
    try {
      setWaitForSubmission(true);
      const base64String = await fileToBase64(selectedFile);
      const jobDescriptionBase64 = await fileToBase64(jdselectedFile);
      // console.log("Base64 String:");
      let body_data = {
        user_id: localStorage.getItem("user_id"),
        resume: base64String,
      };
      if (jobDescriptionBase64) {
        body_data.job_description_base64 = jobDescriptionBase64;
        body_data.job_description_text = null;
      } else if (jdText) {
        body_data.job_description_text = jdText;
        body_data.job_description_base64 = null;
      }

      // console.log("Job Post Request Body:", body_data);

      const response = await fetch(
        "http://************:5002/generate_questions_jd",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(body_data),
        }
      );

      if (response.ok) {
        const data = await response.json();
        setWaitForSubmission(false);
        // console.log("the for jobdiscreptiondatas", data)
        // console.log("the for jobdiinside arrayh", data.jd_questions)
        // Set jd_questions data from response
        if (data.jd_questions) {
          setJdQuestions(data.jd_questions);
        }
      } else {
        console.log("Response not OK:", response.status, response.statusText);
        setWaitForSubmission(false);
      }
    } catch (err) {
      console.log("handle error", err);
      setWaitForSubmission(false);
    }
  };


  const [selectedDomain, setSelectedDomain] = useState(''); // To track selected domain
  const [filteredQuestions, setFilteredQuestions] = useState([]);

  // console.log("jdfrom bot",jdQuestions.length)
  const handleDomainChange = (event) => {
    const domain = event.target.value;
    setSelectedDomain(domain);

    if (domain === "all") {
      // Combine all questions across all domains
      const allQuestionsWithDomain = Object.entries(jdQuestions).map(
        ([domain, questions]) => ({
          domain,
          questions,
        })
      );
      setFilteredQuestions(allQuestionsWithDomain);
    } else if (domain) {
      // Filter questions for the selected domain
      const questions = jdQuestions[domain];
      if (questions) {
        setFilteredQuestions([{ domain, questions }]);
      } else {
        setFilteredQuestions([]);
      }
    } else {
      setFilteredQuestions([]);
    }
  };
  //console.log("foasasfdyttfggh",jdQuestions)
  //  ere start chat bots
  const chatBodyRef = useRef();
  const [showChatbot, setShowChatbot] = useState(false);
  const [chatHistory, setChatHistory] = useState([
    {
      hideInChat: true,
      role: "model",
      text: companyInfo,
    },
  ]);
  const generateBotResponse = async (history) => {
    // Helper function to update chat history
    const updateHistory = (text, isError = false) => {
      setChatHistory((prev) => [...prev.filter((msg) => msg.text != "Thinking..."), { role: "model", text, isError }]);
    };
    // Format chat history for API request
    history = history.map(({ role, text }) => ({ role, parts: [{ text }] }));
    const requestOptions = {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ contents: history }),
    };
    try {
      // Make the API call to get the bot's response
      const response = await fetch(import.meta.env.VITE_API_URL, requestOptions);
      const data = await response.json();
      if (!response.ok) throw new Error(data?.error.message || "Something went wrong!");
      // Clean and update chat history with bot's response
      const apiResponseText = data.candidatesdata[0].content.parts[0].text.replace(/\*\*(.*?)\*\*/g, "$1").trim();
      updateHistory(apiResponseText);
    } catch (error) {
      // Update chat history with the error message
      updateHistory(error.message, true);
    }
  };
  //  useEffect(() => {
  //    // Auto-scroll whenever chat history updates
  //    chatBodyRef.current.scrollTo({ top: chatBodyRef.current.scrollHeight, behavior: "smooth" });
  //  }, [chatHistory]);
  // console.log("existingFiles", existingFiles);



// checkCandidateCount();

  return (
    <div>
      <div className="sidebar">
        <div className="profile">
          <div
            className="image-container"
            ref={imageContainerRef}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            style={{ position: "relative", cursor: "pointer" }}
            onClick={handleImageClicks}
          >
            <img src={profileImage} alt="" />
            {showOptions && (
              <div
                className="options"
                ref={optionsRef}
                style={{
                  position: "absolute",
                  top: "100%",
                  left: "50%",
                  transform: "translateX(-50%)",
                  backgroundColor: "#fff",
                  boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
                  borderRadius: "4px",
                  zIndex: 100,
                }}
              >
                <div
                  className="img_u"
                  onClick={handleUploadClick}
                  style={{
                    padding: "2px",
                    cursor: "pointer",
                    fontSize: "14px",
                    fontFamily: "poppins,sanserif",
                  }}
                >
                  Upload Photo
                </div>
                <div
                  className="img_u"
                  onClick={handleRemovePhoto}
                  style={{
                    padding: "3px",
                    cursor: "pointer",
                    fontSize: "14px",
                    fontFamily: "poppins,sanserif",
                  }}
                >
                  Remove Photo
                </div>
              </div>
            )}
          </div>

          <input
            type="file"
            id="fileInput"
            style={{ display: "none" }}
            accept="image/*"
            onChange={handleFileChange}
          />
        </div>
        <div>
          {userNameCapitalized && (
            <p
              style={{
                color: "#32406D",
                fontSize: "18px",
                fontWeight: "600",
                marginTop: "-24px",
                overflow: "hidden",
                textAlign: "center",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
                maxWidth: "550px", // adjust width as needed to control truncation length
                cursor: "pointer",
              }}
              title={userNameCapitalized} // this will show the full name on hover as a tooltip
            >
              {userNameCapitalized.slice(0, 15)}{/* show first 15 characters */}
              {userNameCapitalized.length > 15 && "..."}
            </p>
          )}
          <p
            style={{
              color: "#32406D",
              fontSize: "15px",
              fontWeight: "500",
              marginTop: "-15px",
            }}
          >
            {USERTYPE === "recruiter" ? "Recruiter" : "Manager"}
          </p>
          <ul>
            <li onClick={toggleSidebar}>
              <Link
                to="/Dashboard"
                onClick={() => {
                  setToInitial()
                  if (localStorage.getItem("page_no")) {
                    localStorage.removeItem("page_no");
                  }
                }}
                className={
                  pathname.toLowerCase() === "/updatecandidate" || pathname.toLowerCase() === "/editcandidate" || pathname.toLowerCase() === "/dashboard"
                    ? "nav-link active"
                    : "nav-link"
                }
              >
                <span>Dashboard</span>
              </Link>
            </li>
            <li onClick={toggleSidebar}>
              <Link
                to="/NewCandidate"
                onClick={() => {
                  setToInitial()
                  if (localStorage.getItem("page_no")) {
                    localStorage.removeItem("page_no");
                  }
                }}
                className={
                  pathname === "/NewCandidate"
                    ? "nav-link active"
                    : "nav-link"
                }
              >
                <span>Peer Assigned Profiles</span>
              </Link>
            </li>
            {USERTYPE === "managment" ? (
              <li onClick={toggleSidebar}>
                <Link
                  onClick={() => {
                    setToInitial()
                    if (localStorage.getItem("page_no")) {
                      localStorage.removeItem("page_no");
                    }
                  }}
                  to="/JobListing"
                  className={
                    pathname === "/JobListing/AddCandidate" || pathname === "/JobListing" || pathname === "/EditJobPosting" || pathname === "/EditJobStatus"
                      ? "nav-link active"
                      : "nav-link"
                  }
                >
                  <span>Job Listing</span>
                </Link>
              </li>
            ) : null}
            {USERTYPE === "managment" ? (
              <li onClick={toggleSidebar}>
                <Link
                  to="/JobAssignments"
                  onClick={() => {
                    setToInitial()
                    if (localStorage.getItem("page_no")) {
                      localStorage.removeItem("page_no");
                    }
                  }}
                  className={
                    pathname === "/JobAssignments"
                      ? "nav-link active"
                      : "nav-link"
                  }
                >
                  <span>Job Assignments</span>
                </Link>
              </li>
            ) : null}
            {USERTYPE === "recruiter" ? (
              <li style={{ position: "relative" }} onClick={toggleSidebar}>
                <Link
                  style={{ paddingRight: "0px" }}
                  onClick={assignRequirementClicked}
                  to="/AssignedRequirements"
                  className={
                    pathname === "/AssignedRequirements/AddCandidate" ||
                      pathname === "/AssignedRequirements"
                      ? "nav-link active"
                      : "nav-link"
                  }
                >
                  <span>Assigned Requirements</span>
                </Link>
                {/* style={{backgroundColor:'red',position:'fixed',top:0,right:0,padding:'10px',borderRadius:'5px'}} */}

                {notificationCount !== 0 && (
                  <div
                    style={{
                      position: "absolute",
                      width: "10px",
                      backgroundColor: "red",
                      top: "1px",
                      color: "white",
                      right: "2px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      padding: "4px 12px 4px 12px",
                      borderRadius: "16px",
                      fontSize: "12px",
                    }}
                  >
                    <div>{notificationCount}</div>
                  </div>
                )}
              </li>
            ) : null}
            <li onClick={toggleSidebar}>
              <Link
                to="/RegisterCandidate"
                onClick={() => {
                  setToInitial()
                  if (localStorage.getItem("page_no")) {
                    localStorage.removeItem("page_no");
                  }
                }}
                className={
                  pathname === "/RegisterCandidate/AddCandidate" ||
                    pathname === "/RegisterCandidate"
                    ? "nav-link active"
                    : "nav-link"
                }
              >
                <span>Register Candidate</span>
              </Link>
            </li>
            {/* <li onClick={toggleSidebar}>
              <Link
                to="/ResumeSearching"
             
                className={
                  pathname === "/ResumeSearching"
                    ? "nav-link active"
                    : "nav-link"
                }
              >
                <span>Resume Portal</span>
              </Link>
            </li> */}


            < ResumeUpload
              ResumeModal={ResumeModal}

              handleCloseResume={handleCloseResume} />

            {USERTYPE === "managment" ? (
              <li onClick={toggleSidebar}>
                <Link
                  onClick={() => {
                    setToInitial()
                    if (localStorage.getItem("page_no")) {
                      localStorage.removeItem("page_no");
                    }
                  }}
                  to="/ProfileTransfer"
                  className={
                    pathname === "/ProfileTransfer"
                      ? "nav-link active"
                      : "nav-link"
                  }
                >
                  <span>Profile Transfer</span>
                </Link>
              </li>
            ) : null}


            {/* {USERTYPE === "managment" ? (
              <li onClick={toggleSidebar}>
                <Link
                  to="/Targetfix"
                  onClick={() => {
                    setToInitial()
                    if (localStorage.getItem("page_no")) {
                      localStorage.removeItem("page_no");
                    }
                  }}
                  className={
                    pathname === "/Targetfix"
                      ? "nav-link active"
                      : "nav-link"
                  }
                >
                  <span>Target Assign</span>
                </Link>
              </li>
            ) : null} */}
            {/* <li onClick={toggleSidebar}>
              <Link
                to="/ResumeFetch"
                // onClick={() => {
                //   setToInitial()
                //   if (localStorage.getItem("page_no")) {
                //     localStorage.removeItem("page_no");
                //   }
                // }}
                className={
                  pathname === "/ResumeFetch"
                    ? "nav-link active"
                    : "nav-link"
                }
              >
                <span>Candidate Portal</span>
              </Link>
            </li> */}
            <li onClick={toggleSidebar}>
              <Link
                to="/NewReport"
                // onClick={() => {
                //   setToInitial()
                //   if (localStorage.getItem("page_no")) {
                //     localStorage.removeItem("page_no");
                //   }
                // }}
                className={
                  pathname === "/NewReport"
                    ? "nav-link active"
                    : "nav-link"
                }
              >
                <span>Analytics</span>
              </Link>
            </li>

            {/* {USERTYPE === "managment" ? (
              <li onClick={toggleSidebar}>
                <Link
                  to="/AccountCreation"
                  onClick={() => {
                    setToInitial()
                    if (localStorage.getItem("page_no")) {
                      localStorage.removeItem("page_no");
                    }
                  }}
                  className={
                    pathname === "/AccountCreation"
                      ? "nav-link active"
                      : "nav-link"
                  }
                >
                  <span>Account Creation</span>
                </Link>
              </li>
            ) : null} */}
            {/* {USERTYPE === "managment" ? (
              <li onClick={toggleSidebar}>
                <Link
                  to="/stoxxo"
                  onClick={() => {
                    setToInitial()
                    if (localStorage.getItem("page_no")) {
                      localStorage.removeItem("page_no");
                    }
                  }}
                  className={
                    pathname === "/stoxxo"
                      ? "nav-link active"
                      : "nav-link"
                  }
                >
                  <span>Stoxxo</span>
                </Link>
              </li>
            ) : null} */}






            {USERTYPE === "managment" ? (
              <li onClick={toggleSidebar}>
                <Link
                  to="/UserAccounts"
                  onClick={() => {
                    setToInitial()
                    if (localStorage.getItem("page_no")) {
                      localStorage.removeItem("page_no");
                    }
                  }}
                  className={
                    pathname === "/AccountDeactivation" ||
                      pathname === "/AccountCreation" ||
                      pathname === "/UserAccounts"
                      ? "nav-link active"
                      : "nav-link"
                  }
                >
                  <span>User Accounts</span>
                </Link>
              </li>
            ) : null}


            {USERTYPE === "recruiter" ? (
              <li onClick={toggleSidebar}>
                <Link
                  to="/OverView"
                  // onClick={() => {
                  //   setToInitial()
                  //   if (localStorage.getItem("page_no")) {
                  //     localStorage.removeItem("page_no");
                  //   }
                  // }}
                  className={
                    pathname === "/OverView"
                      ? "nav-link active"
                      : "nav-link"
                  }
                >
                  <span>Profile Analysis</span>
                </Link>
              </li>
            ) : null}
            <li onClick={toggleSidebar}>
              <Link
                to="/ChangePassword"
                onClick={() => {
                  setToInitial()
                  if (localStorage.getItem("page_no")) {
                    localStorage.removeItem("page_no");
                  }
                }}
                className={
                  pathname === "/ChangePassword"
                    ? "nav-link active"
                    : "nav-link"
                }
              >
                <span>Change Password</span>
              </Link>
            </li>


           

            <li onClick={handleCalendar} className={`logoutBtn ${showCalendar ? "active" : ""}`}>
              <span id="lgout" className={`logouBtn ${showCalendar ? "actve" : ""}`}>Calendar</span>
            </li>
            < ScheduleMeet
              interviewModal={interviewModal}
              start_autoDate={start_autoDate}
              end_autoDate={end_autoDate}
              startautoTime={startautoTime}
              endautoTime={endautoTime}
              //  resetForm={resetForm}
              setShowCalendar={setShowCalendar}
              InterviewcloseModal={InterviewcloseModal} />
       
               <li onClick={toggleSidebar}>
              <Link
                to="/RaiseIssue"
                // onClick={() => {
                //   setToInitial()
                //   if (localStorage.getItem("page_no")) {
                //     localStorage.removeItem("page_no");
                //   }
                // }}
                className={
                  pathname === "/RaiseIssue"
                    ? "nav-link active"
                    : "nav-link"
                }
              >
                <span>Help & Support</span>
              </Link>
            </li>

                    <li onClick={handleLogout} className={`logoutBtn ${showModal ? "active" : ""}`}>
              <span id="lgout" className={`logouBtn ${showModal ? "actve" : ""}`}>Logout</span>
            </li>
            
            {
              localStorage.getItem("user_type") === "recruitment" && (<li style={{ paddingTop: "50px" }}>
                <ChatBotComponent setTelephonic={setTelephonic} msgs={pathname === "/Dashboard" ? msgs : []} chatMsgId={chatMsgId} />
              </li>)
            }

          
            {/* { localStorage.getItem("user_type") === "management" && (
              <li>
           <div className={`contaiiner ${showChatbot ? "show-chatbot" : ""}`} style={{marginLeft:"5px"}}>
               <button onClick={() => setShowChatbot((prev) => !prev)} id="chatbot-toggler">
                 <span className="material-symbols-rounded" style={{fontSize:"30px",marginTop:"10px"}} ><IoChatboxEllipses /></span>
                 <span className="material-symbols-rounded" style={{fontSize:"30px",marginTop:"10px"}} ><IoClose /></span>
               </button>
               <div className="chatbot-popup" >
          
                 <div className="chat-header">
                   <div className="header-info" >
                     <ChatbotIcon />
                     <h2 className="logo-text">Chatbot</h2>
                   </div>
                   <button onClick={() => setShowChatbot((prev) => !prev)} className="material-symbols-rounded">
                   <IoIosArrowDown />
                   </button>
                 </div>
          
                 <div ref={chatBodyRef} className="chat-body">
                   <div className="message bot-message">
                     <ChatbotIcon />
                     <p className="message-text" style={{color:"#000"}}>
                       Hey there  <br /> How can I help you?
                     </p>
                   </div>
          
                   {chatHistory.map((chat, index) => (
                     <ChatMessage key={index} chat={chat} />
                   ))}
                 </div>
  
                 <div className="chat-footer">
                   <ChatForm chatHistory={chatHistory} setChatHistory={setChatHistory} generateBotResponse={generateBotResponse} />
                 </div>
               </div>
             </div>    </li>)
            } */}
          </ul>
        </div>
        <Modal
          isOpen={showModal}
          onRequestClose={handleCloseModal}
          contentLabel="Logout Confirmation"
          className="modal-content"
          overlayClassName="modal-overlay"
          style={{
            overlay: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              backdropFilter: "blur(0.5px)",
              zIndex: 9999,
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            },
            content: {
              width: "270px",
              height: "110px",
              margin: "auto",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              background: "white",
              borderRadius: "10px",
              boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.5)",
              padding: "20px 20px 10px",
            },
          }}
        >
          <div className="modal-actions" style={{ marginBottom: "35px" }}>
            <p
              style={{
                fontSize: "17px",
                fontFamily: "roboto",
                fontWeight: "400",
                color: "black",
              }}
            >
              Are you sure you want to logout?
            </p>
          </div>
          <div style={{ marginTop: "-40px" }}>
            <button
              onClick={handleCloseModal}
              style={{
                marginRight: "30px",
                backgroundColor: "green",
                color: "white",
                borderRadius: "5px",
                border: "none",
                padding: "5px",
                cursor: "pointer",
              }}
            >
              Cancel
            </button>
            <button
              onClick={() => {
                handleConfirmLogout(1);
              }}
              style={{
                backgroundColor: "Red",
                color: "white",

                border: "none",
                width: "60px",
                padding: "5px",
                borderRadius: "5px",
                cursor: "pointer",
              }}
            >
              Logout
            </button>
          </div>
        </Modal>
        <Modal
          isOpen={isCropping}
          contentLabel="Crop Image"
          className="modal-content"
          overlayClassName="modal-overlay"
          style={{
            overlay: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              zIndex: 9999,
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            },
            content: {
              width: "80%",
              maxWidth: "500px",
              height: "400px",
              background: "white",
              borderRadius: "10px",
              boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.5)",
              padding: "20px",
            },
          }}
        >
          <div style={{ position: "relative", width: "100%", height: "100%" }}>
            <Cropper
              image={imageForCropping}
              crop={crop}
              zoom={zoom}
              aspect={1}
              onCropChange={setCrop}
              onCropComplete={(croppedArea, croppedAreaPixels) =>
                setCroppedAreaPixels(croppedAreaPixels)
              }
              onZoomChange={setZoom}
            />
            <div
              style={{
                position: "absolute",
                bottom: "10px",
                left: "50%",
                transform: "translateX(-50%)",
                zIndex: 1,
              }}
            >
              <Slider
                value={zoom}
                min={1}
                max={3}
                step={0.1}
                aria-labelledby="Zoom"
                onChange={(e, zoom) => setZoom(zoom)}
                style={{ width: "200px" }}
              />
            </div>
            <div style={{ textAlign: "center", marginLeft: "30px" }}>
              <button
                onClick={handleSaveCroppedImage}
                style={{
                  marginRight: "30px",
                  height: "30px",
                  width: "60px",
                  color: "white",
                  backgroundColor: "green",
                  borderRadius: "5px",
                  border: "none",
                }}
              >
                Save
              </button>
              <button
                onClick={() => setIsCropping(false)}
                style={{
                  marginRight: "30px",
                  height: "30px",
                  width: "60px",
                  color: "white",
                  backgroundColor: "red",
                  borderRadius: "5px",
                  border: "none",
                }}
              >
                Cancel
              </button>
            </div>
          </div>
        </Modal>
        <Modal
          isOpen={Telephonic}
          onRequestClose={handleCloseModal}
          contentLabel="Logout Confirmation"
          className="modal-content"
          id="QuestionModal"
          overlayClassName="modal-overlay"
          style={{
            overlay: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              zIndex: 9999,
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            },
            content: {
              width: "57%",
              maxHeight: "98%",

              margin: "auto",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              background: "#f7f7f7",
              borderRadius: "10px",
              boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.5)",
              padding: "20px 40px",
              textAlign: "center",
            },
          }}
        >
          <div className="Modalleft" style={{ display: "flex", justifyContent: "space-between", paddingLeft: "70px" }}>
            <h2 className="Modalheading" style={{ marginBottom: "20px", color: "#32406D" }}>Recruiter-Candidate Interaction Support</h2>
            <MdCancel onClick={handleCloseModal} style={{ cursor: "pointer", color: "#32406d", height: "30px", width: "30px" }} />
          </div>
          <div style={{ marginBottom: "10px", textAlign: "left" }}>
            <label htmlFor="generateQuestions" style={{ fontWeight: "bold", marginRight: "10px", textAlign: "left" }}>
              JD/RESUME:
            </label>
            <select
              style={{ borderRadius: "5px", border: "1px solid #aaa", width: window.innerWidth <= 542 ? '100%' : '50%' }}
              onChange={(e) => {
                setSelectedOptions(e.target.value);
                setFilteredQuestions(null);
                setSelectedDomain(null);
                setJdQuestions([]);

              }}
            >
              <option value="" disabled selected>
                Select an option
              </option>
              <option value="Job_Description">Job Description</option>
              <option value="Resume">Resume</option>
            </select>
          </div>
          {selectedOptions === "Job_Description" && (
            <form className="leftform" onSubmit={handleSubmitJD}>
              {selectedOptions === "Job_Description" && (
                <>
                  {/* Job Description File Upload */}
                  <div style={{ marginBottom: "10px", textAlign: "left" }}>
                    <label htmlFor="jobDescriptionFile" style={{ fontWeight: "bold", marginRight: "10px", textAlign: "left" }}>
                      Job Description
                    </label>
                    <input
                      style={{ padding: "10px", borderRadius: "5px", width: "50%", border: "1px solid #aaa", width: window.innerWidth <= 542 ? '100%' : '400px' }}
                      type="file"
                      name="jobDescriptionFile"
                      id="jobDescriptionFile"
                      accept=".pdf,.doc,.docx"
                      onChange={handleJobdescriptionchange}
                    />
                  </div>

                  {/* Job Description Text Area */}
                  <div style={{ marginBottom: "2px", textAlign: "left" }}>
                    <label htmlFor="jdText" style={{ fontWeight: "bold", marginRight: "10px", textAlign: "left" }}>
                      Job Description Text:
                    </label>
                    <textarea
                      style={{ padding: "10px", borderRadius: "5px", border: "1px solid #aaa", width: "70%", height: "100px", }}
                      id="jdText"
                      rows="6"
                      placeholder="Enter job description here"
                    ></textarea>
                  </div>
                </>
              )}
              {selectedOptions === "Job_Description" && jdQuestions && Object.keys(jdQuestions).length > 0 && (

                <div style={{ padding: "10px", textAlign: "left" }}>
                     <label htmlFor="generateQuestions" style={{ fontWeight: "bold", marginRight: "10px" }}>Types Of Questions:</label>
                  <select
                    value={selectedDomain || ""}
                    onChange={handleDomainChange}
                    style={{ padding: "5px", fontSize: "14px" }}
                  >
                    <option value="">Select a Domain</option>
                    <option value="all">Select All</option>
                    {Object.keys(jdQuestions).map((domain, index) => (
                      <option key={index} value={domain}>
                        {domain}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {selectedDomain && filteredQuestions.length > 0 ? (
                <div style={{ height: "110px", overflowY: "auto", padding: "10px", textAlign: "left" }}>
                  {filteredQuestions.map((group, index) => (
                    <div key={index}>
                      <h4 style={{ fontSize: "16px", marginBottom: "10px", textAlign: "left" }}>
                        {group.domain}:
                      </h4>
                      <ol style={{ paddingLeft: "20px", fontSize: "14px", textAlign: "left" }}>
                        {group.questions.map((question, qIndex) => (
                          <li key={qIndex}>
                            {qIndex + 1}) {question.replace(/[{}"\[\]]/g, '')} {/* Clean question text */}
                          </li>
                        ))}
                      </ol>
                    </div>
                  ))}
                </div>
              ) : selectedDomain ? (
                <p style={{ textAlign: "left", fontSize: "16px", color: "gray" }}>
                  No questions available for the selected domain.
                </p>
              ) : null}
              <div style={{ display: "flex", justifyContent: "space-between", marginTop: "10px" }}>
                <button id="addCandidateSubmit"

                  type="submit"
                  style={{
                    borderRadius: "4px",
                    background: "#32406D",
                    color: "#fff",
                    width: "100px",
                    position: "relative",
                  }}
                >
                  {waitForSubmission ? "" : "Submit"}
                  <ThreeDots
                    wrapperClass="ovalSpinner"
                    wrapperStyle={{
                      position: "absolute",
                      top: "50%",
                      left: "50%",
                      transform: "translate(-50%, -50%)",
                    }}
                    visible={waitForSubmission}
                    height="45"
                    width="45"
                    color="white"
                    ariaLabel="oval-loading"
                  />
                </button>
                <button
                  type="button"
                  onClick={handleDownloadQuestions}
                  style={{
                    borderRadius: '4px',
                    background: '#32406D',
                    color: '#fff',
                    width: '150px',
                    // padding: '10px',
                    border: 'none',
                    cursor: 'pointer',
                    // marginTop: '20px',
                  }}
                >
                  Download Word
                </button>
              </div>
            </form>
          )}
          {selectedOptions === "Resume" && (
            <form className="leftform" onSubmit={handleSubmit}>
              <div style={{ marginBottom: "10px", textAlign: "left" }}>
                <label htmlFor="resume" style={{ fontWeight: "bold", marginRight: "10px" }}>Upload Resume:</label>
                <input
                  style={{ padding: "10px", borderRadius: "5px", border: "1px solid #aaa" }}
                  type="file"
                  name="resume"
                  id="resume"
                  accept=".pdf,.doc,.docx"
                  onChange={handleresumeChange}
                />
              </div>
              <div style={{ marginBottom: "20px", textAlign: "left" }}>
                <label htmlFor="generateQuestions" style={{ fontWeight: "bold", marginRight: "10px" }}>Generate Question:</label>
                <select
                  id="generateQuestions"
                  value={selectedOption}
                  onChange={handleSelectChange}
                  style={{ borderRadius: "5px", border: "1px solid #aaa", width: "100%" }}
                >
                  <option value="" disabled>Select an option</option>
                  <option value="Generate five questions each for location preference, availability to start, salary expectations, and questions for the recruiter.">
                    Generate five questions each for location preference, availability to start, salary expectations, and questions for the recruiter.
                  </option>
                  {/* <option value="Generate five questions each for location preference, availability to start, salary expectations, and questions for the recruiter with answers.">
                  Generate five questions each for location preference, availability to start, salary expectations, and questions for the recruiter with answers.
                </option> */}
                  <option value="Generate five technical questions for each skill in the resume.">
                    Generate five technical questions for each skill in the resume.
                  </option>
                  {/* <option value="Generate five technical questions for each skill in the resume with answers.">
                  Generate five technical questions for each skill in the resume with answers.
                </option> */}
                  {/* <option value="Generate five Questions related to only certification in the resume">
                  Generate five Questions related to only certification in the resume
                </option>
                <option value="Generate five questions about the productivity tools a candidate is proficient with, as listed in their resume">
                  Generate five questions about the productivity tools a candidate is proficient with, as listed in their resume
                </option>
                <option value="Generate five questions about how a candidate found and applied for this job">
                  Generate five questions about how a candidate found and applied for this job
                </option> */}
                </select>
              </div>
              {interviewData && interviewData.response && interviewData.response[0] && (
                <div >
                  <div style={{ marginBottom: "20px", textAlign: "left" }}>
                    <label htmlFor="generateQuestions" style={{ fontWeight: "bold", marginRight: "10px" }}>Types Of Questions:</label>
                    <select className="selectQ" value={general} onChange={handlegeneralchange}>
                      <option value="">Select a category</option>
                      {
                        Object.keys(interviewData.response[0].questions)?.length > 0 && (<option value="select all">select all</option>)
                      }
                      {Object.keys(interviewData.response[0].questions).map((general, index) => (
                        <option key={index} value={general}>{general}</option>
                      ))}
                    </select>
                  </div>
                  <div style={{ textAlign: "left", color: "green", fontSize: "14px" }}>{interviewData.response[0].heading}</div>
                </div>

              )}
              <div >
                {general && general === "select all" ? (
                  <div style={{ textAlign: "left", overflow: "auto", height: "110px" }}>
                    <ul>
                      {selectAllData?.map((question, index) => (
                        <li key={index}
                          style={{
                            fontSize: indexes && indexes.includes(index) ? "16px" : "14px",
                            fontWeight: indexes && indexes.includes(index) ? "bold" : "normal"
                          }}
                        >{cleanQuestion(question)}
                        </li>
                      ))}
                    </ul>
                  </div>
                ) : (
                  <div style={{ textAlign: "left", overflow: "auto", height: '110px' }}>
                    <ul>
                      {interviewData?.response[0]?.questions[general]?.map((question, index) => (
                        <li key={index}>{cleanQuestion(question)}</li>

                      ))}
                    </ul>
                  </div>
                )}
              </div>
              <div style={{ display: "flex", justifyContent: "space-between", marginTop: "10px" }}>
                <button id="addCandidateSubmit"

                  type="submit"
                  style={{
                    borderRadius: "4px",
                    background: "#32406D",
                    color: "#fff",
                    width: "100px",
                    position: "relative",
                  }}
                >
                  {waitForSubmission ? "" : "Submit"}
                  <ThreeDots
                    wrapperClass="ovalSpinner"
                    wrapperStyle={{
                      position: "absolute",
                      top: "50%",
                      left: "50%",
                      transform: "translate(-50%, -50%)",
                    }}
                    visible={waitForSubmission}
                    height="45"
                    width="45"
                    color="white"
                    ariaLabel="oval-loading"
                  />
                </button>
                <button
                  type="button"
                  onClick={handleDownloadQuestions}
                  style={{
                    borderRadius: '4px',
                    background: '#32406D',
                    color: '#fff',
                    width: '150px',
                    // padding: '10px',
                    border: 'none',
                    cursor: 'pointer',
                    // marginTop: '20px',
                  }}
                >
                  Download Word
                </button>
              </div>


            </form>
          )}


        </Modal>
        <Modal
          isOpen={innerModal}
          onRequestClose={handleCloseInnerModal}
          contentLabel="Logout Confirmation"
          className="modal-content_some"
          overlayClassName="modal-overlay"
          style={{
            overlay: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              zIndex: 9999,
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            },
            content: {
              width: "30%",
              height: "80px",
              margin: "auto",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              background: "#f7f7f7",
              borderRadius: "10px",
              boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.5)",
              padding: "20px 40px",
              textAlign: "center",

            },
          }}
        >
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div style={{ color: "red", marginTop: "10px" }}>
              something went wrong, please try again
            </div>
            <div style={{ textAlign: "right" }}>
              <MdCancel onClick={handleCloseInnerModal} style={{ cursor: "pointer", height: "30px", width: "30px", color: "#32406D", marginTop: "8px" }} />
            </div>
          </div>

        </Modal>
        <Modal
          isOpen={isOpen || showCalendar}
          onRequestClose={() => {
            setIsOpen(false);
            setShowCalendar(false);
          }}
          style={{
            overlay: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              zIndex: 9999,
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            },
            content: {
              width: "60%",
              height: "80%",
              margin: "auto",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              background: "#f7f7f7",
              borderRadius: "10px",
              boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.5)",
              padding: "20px 40px",
              textAlign: "center",

            },
          }}>
          {/* <button onClick={closeModal} style={{ position: 'absolute', top: '10px', right: '10px' }}>Close</button> */}
          <MdCancel onClick={closeModal} style={{ cursor: "pointer", position: 'absolute', top: '10px', right: '10px', color: "#32406d", height: "30px", width: "30px" }} />

          <div style={{ height: 'calc(100% - 40px)' }}>
            <h2>Calendar</h2>
            <Calendar
              localizer={localizer}
              events={transformedEvents}
              startAccessor="start"
              endAccessor="end"
              style={{ height: '100%' }}
              views={['month', 'week', 'day']}
              formats={formats}
              onSelectEvent={handleEventClick}
              selectable
              onSelectSlot={handleEmptySlotClick}
            />
          </div>
        </Modal>

        <Modal
          isOpen={showmeet}
          onRequestClose={closemeet}
          contentLabel="Logout Confirmation"
          className="modal-content"
          id="QuestionModal"
          overlayClassName="modal-overlay"
          style={{
            overlay: {
              backgroundColor: "transparent",
              zIndex: 9999,
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            },
            content: {
              maxHeight: "98%",
              margin: "auto",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              background: "#f7f7f7",
              borderRadius: "10px",
              boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.5)",
              padding: "20px 15px",
              textAlign: "center",
            },
          }}
        >
          <div className="Modalleft" style={{ display: "flex", justifyContent: "space-between", paddingLeft: "0px" }}>
            <h2 className="Modalheading" style={{ margin: "5px 0px", fontSize: "15px", color: "#000" }}>My Calendar</h2>
            {/* <MdCancel onClick={closemeet} style={{ cursor: "pointer", color: "#32406d", height: "30px", width: "30px" }} /> */}
          </div>

          <div className="Modalleft" style={{ display: "flex", justifyContent: "space-between", paddingLeft: "0px" }}>
            <h2 className="Modalheading" style={{ marginBottom: "10px", color: "rgb(70 68 68 / 95%)" }}>{selectedEvent?.title}</h2>
            {/* <MdCancel onClick={closemeet} style={{ cursor: "pointer", color: "#32406d", height: "30px", width: "30px" }} /> */}
          </div>
          <form className="leftform" onSubmit={handleSubmit}>
            <div style={{ marginBottom: "10px", textAlign: "left" }}>
              <label htmlFor="resume" style={{ fontWeight: "bold", marginRight: "10px", color: 'rgb(63 63 63)' }}>
                {selectedEvent ? `${formattedStartDate}, ${formattedStartTime} - ${formattedEndTime}` : ""}
              </label>
            </div>

            <div style={{ display: "flex", justifyContent: "Left", marginTop: "10px" }}>
              {selectedEvent && selectedEvent.join_url && (
                <a
                  href={selectedEvent.join_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    borderRadius: "4px",
                    background: "#32406D",
                    color: "#fff",
                    padding: "10px",
                    width: "100px",
                    position: "relative",
                  }}
                >
                  Join
                </a>
              )}
              {selectedEvent && (
                <>
                  {/* {console.log('Local storage email:', localStorage.getItem('email'))}
                  {console.log('Event recruiter email:', selectedEvent.rec_email)}
                  {console.log('Emails match:', localStorage.getItem('email')?.toLowerCase() === selectedEvent.rec_email?.toLowerCase())} */}

                  {localStorage.getItem('email')?.toLowerCase() === selectedEvent.rec_email?.toLowerCase() && (
                    <>
                      <button
                        type="button"
                        onClick={() => {
                          EditopenModal();
                          setShowCalendar(false);
                        }}
                        style={{
                          borderRadius: '4px',
                          marginLeft: '5px',
                          background: 'transparent',
                          border: "1px solid #000",
                          color: '#000',
                          width: '90px',
                          cursor: 'pointer',
                        }}
                      >
                        Edit
                      </button>
                      {!waitForSubmissiondel ? (
                        <button
                          type="button"
                          onClick={() => handleDeletemeet(selectedEvent.meeting_id)}
                          style={{
                            borderRadius: '4px',
                            marginLeft: '5px',
                            background: 'transparent',
                            border: "1px solid #000",
                            color: 'rgb(238 16 16)',
                            width: '40px',
                            fontSize: "20px",
                            cursor: 'pointer',
                          }}
                        >
                          <MdDelete />
                        </button>
                      ) : (
                        <div style={{ marginLeft: "10px" }}>
                          <TailSpin
                            visible={true}
                            height="40"
                            width="40"
                            color="#4fa94d"
                            ariaLabel="tail-spin-loading"
                            radius="1"
                            wrapperStyle={{}}
                            wrapperClass=""
                          />
                        </div>
                      )}
                    </>
                  )}
                </>
              )}
            </div>

            <div style={{ display: "flex", justifyContent: "Left", marginTop: "10px" }}>
              <span style={{ borderRadius: "4px", fontSize: "18px", color: "#32406D", position: "relative" }}>
                <IoLocationOutline />
              </span>
              <h4 style={{ marginLeft: '5px', color: '#000' }}>
                ATS Meet Connect
              </h4>
            </div>
          </form>
        </Modal>



        <Modal
          isOpen={EditModal}
          onRequestClose={EditcloseModal}
          contentLabel="Edit Modal"
          style={{
            overlay: {
              backgroundColor: 'rgba(0, 0, 0, 0.5)', // Slightly lighter overlay
              zIndex: 9999,
            },
            content: {
              color: '#333', // Darker text for better readability
              top: '50%',
              left: '50%',
              right: 'auto',
              bottom: 'auto',
              marginRight: '-50%',
              transform: 'translate(-50%, -50%)',
              width: window.innerWidth <= 542 ? '100%' : '550px', // More compact width
              maxHeight: '610px', // Restrict maximum height
              padding: '0px', // Increased padding
              borderRadius: '8px', // Rounded corners
              boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)', // Light shadow for depth
              position: 'relative', // For absolute positioning of buttons
            }
          }}
        >
          <form onSubmit={handleFormSubmit}>
            <div style={{ padding: "5px 15px" }}>
              <div className="shedulehed">
                <h2 style={{ color: "#32406D", fontSize: '20px', textAlign: 'center', marginLeft: "-20px" }}>Edit Meeting</h2>

              </div>
              <div style={{ marginBottom: '5px' }}>
                <label style={{ display: 'block', marginBottom: '4px', fontSize: '16px', textAlign: "left" }}>Title</label>
                <input
                  type="text"
                  placeholder="Add title"
                  value={selectedEvent?.title || ''}
                  style={{ width: '100%', height: "35px", borderRadius: "5px", border: "1px solid gray", paddingLeft: "10px" }}
                  onChange={(e) => setSelectedEvent({ ...selectedEvent, title: e.target.value })}

                />
              </div>
              <div style={{ marginBottom: '5px' }}>
                <label style={{ display: 'block', marginBottom: '4px', fontSize: '16px', textAlign: "left" }}>
                  Attendees
                </label>
                <div ref={inputRef} style={{ position: 'relative' }}>
                  <div style={{
                    display: 'flex',
                    flexWrap: 'nowrap',
                    alignItems: 'center',
                    padding: '5px',
                    border: '1px solid gray',
                    borderRadius: '5px',
                    cursor: 'text',
                    height: '40px',
                    overflowX: 'auto',
                    overflowY: 'hidden',
                    position: 'sticky',
                    right: '0px'
                  }}>
                    {selectedEmails1.map((email, idx) => (
                      <span key={idx} style={{
                        display: 'inline-flex',
                        alignItems: 'center',
                        backgroundColor: '#007bff',
                        color: 'white',
                        padding: '0px 10px',
                        borderRadius: '20px',
                        marginRight: '5px',
                        fontSize: '14px',
                        marginTop: '5px'
                      }}>
                        {email}
                        <button
                          type="button"
                          onClick={() => handleCheckboxChange(email)}
                          style={{
                            background: 'transparent',
                            border: 'none',
                            color: 'white',
                            marginLeft: '8px',
                            cursor: 'pointer',
                            fontSize: '16px'
                          }}
                        >
                          &times;
                        </button>
                      </span>
                    ))}
                    <input
                      type="text"
                      placeholder="Select or search emails"
                      style={{
                        flex: '1',
                        minWidth: '300px',
                        border: 'none',
                        paddingLeft: '10px',
                        fontSize: '16px',
                        outline: 'none',
                        width: 'auto'
                      }}
                      value={searchQuery}
                      // onFocus={() => {
                      //   console.log('Input focused');
                      //   handleFocus();
                      // }}
                      // onBlur={() => {
                      //   console.log('Input blurred');
                      //   handleBlur();
                      // }}
                      onChange={(e) => {
                        console.log('Input changed:', e.target.value);
                        handleSearchChange(e);
                      }}
                    />
                    <button
                      type="button"
                      onClick={() => {
                        console.log('Add button clicked');
                        handleAddNewEmail();
                      }}
                      style={{
                        position: 'sticky',
                        right: '0px',
                        backgroundColor: '#007bff',
                        color: 'white',
                        border: 'none',
                        padding: '5px 10px',
                        borderRadius: '4px',
                        whiteSpace: 'normal',
                        zIndex: '1',
                        display: filteredEmails.length === 0 && searchQuery.trim() ? 'inline-block' : 'none'
                      }}
                    >
                      Add
                    </button>
                  </div>
                  {isDropdownOpen1 && (
                    <div className="dropdown-menu" style={{
                      border: '1px solid #ccc',
                      borderRadius: '4px',
                      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
                      marginTop: '5px',
                      width: "100%",
                      position: 'absolute',
                      zIndex: 1000,
                      background: 'white',
                      // width: 'auto'
                    }}>
                      {filteredEmails.length ? (
                        filteredEmails.map((email, idx) => (
                          <label key={idx} className="dropdown-item" style={{
                            display: 'flex',
                            padding: '5px',
                            cursor: 'pointer',
                            fontWeight: "normal",
                          }}>
                            <input
                              type="checkbox"
                              value={email}
                              checked={selectedEmails1.includes(email)}
                              onChange={(e) => {
                                // console.log('Checkbox changed:', e.target.value, e.target.checked);
                                handleCheckboxChange(email);
                                setIsDropdownOpen1(false);
                                setSearchQuery("");
                              }}
                              style={{ marginRight: '5px' }}
                            />
                            {email}
                          </label>
                        ))
                      ) : (
                        <div style={{ padding: '5px' }}>
                          No matching emails found
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
              <div style={{ marginBottom: '5px' }}>
                <label style={{ display: 'block', marginBottom: '4px', fontSize: '16px', textAlign: "left" }}>
                  Attendees (optional)
                </label>
                <div ref={dropdownRef} style={{ position: 'relative' }}>
                  <div ref={inputRef} style={{
                    display: 'flex',
                    flexWrap: 'nowrap',
                    alignItems: 'center',
                    padding: '5px',
                    border: '1px solid gray',
                    borderRadius: '5px',
                    cursor: 'text',
                    height: 'auto',
                    // overflow: 'hidden',
                    position: 'sticky',
                    overflow: 'auto',
                    right: '0px'
                  }}>
                    {selectedEmails.map((email, idx) => (
                      <span key={idx} style={{
                        display: 'inline-flex',
                        alignItems: 'center',
                        backgroundColor: '#007bff',
                        color: 'white',
                        padding: '0px 10px',
                        borderRadius: '20px',
                        marginRight: '5px',
                        fontSize: '14px',
                        marginTop: '5px'
                      }}>
                        {email}
                        <button
                          onClick={() => handleEmailChange1(email)}
                          style={{
                            background: 'transparent',
                            border: 'none',
                            color: 'white',
                            marginLeft: '5px',
                            cursor: 'pointer'
                          }}
                        >
                          &times;
                        </button>
                      </span>
                    ))}
                    <input
                      type="text"
                      placeholder="Select or search emails"
                      style={{
                        flex: '1',
                        minWidth: '300px',
                        border: 'none',
                        paddingLeft: '10px',
                        fontSize: '16px',
                        outline: 'none',
                        width: 'auto'
                      }}
                      value={searchQuerys}
                      onClick={toggleDropdown}
                      onChange={handleInputChange}
                    />
                    {isDropdownOpen && searchQuerys && filteredManagers.length === 0 && filteredRecruiters.length === 0 && (
                      <button
                        type="button"
                        onClick={handleAddManualEmail}
                        style={{
                          marginLeft: '10px',
                          backgroundColor: '#007bff',
                          color: 'white',
                          border: 'none',
                          borderRadius: '5px',
                          padding: '5px 10px',
                          cursor: 'pointer',
                          position: 'sticky',
                          right: '0px'
                        }}
                      >
                        Add
                      </button>
                    )}
                  </div>
                  {isDropdownOpen && (filteredManagers.length > 0 || filteredRecruiters.length > 0) && (
                    <div className="dropdown-menu" style={{
                      border: '1px solid #ccc',
                      borderRadius: '4px',
                      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
                      marginTop: '5px',
                      width: '100%',
                      position: 'absolute',
                      zIndex: 1000,
                      background: 'white',
                      maxHeight: '150px',
                      overflowY: 'auto',
                      // width: 'auto'
                    }}>
                      {[...new Set([...filteredManagers, ...filteredRecruiters].map(person => person.email))].map(email => (
                        <label key={email} className="dropdown-item" style={{
                          display: 'flex',
                          padding: '5px',
                          cursor: 'pointer',
                   
                          fontWeight: "normal",
                          alignItems: 'center'
                        }}>
                          <input
                            type="checkbox"
                            checked={selectedEmails.includes(email)}
                            onChange={() => handleEmailChange1(email)}
                            style={{ marginRight: '5px' }}
                          />
                          {email}
                        </label>
                      ))}
                      {/* {filteredManagers.length === 0 && filteredRecruiters.length === 0 && (
                        <div style={{ padding: '5px' }}>
                          No matching emails found
                        </div>
                      )} */}
                    </div>
                  )}
                </div>
              </div>
              <div>
                <label style={{ display: 'block', marginBottom: '4px', fontSize: '16px', textAlign: "left" }}>Description</label>
                <textarea
                  placeholder="Enter your text here"
                  rows="4"
                  cols="50"
                  style={{
                    flex: '1',
                    minWidth: '100%',
                    border: '1px solid lightgray',
                    paddingLeft: '10px',
                    fontSize: '16px',
                    height: '40px',
                  }}
                  value={selectedEvent?.description}
                  onInput={(e) => {
                    e.target.style.height = 'auto'; // Reset height to auto
                    e.target.style.height = `${e.target.scrollHeight}px`; // Adjust height based on scrollHeight
                  }}
                  onChange={(e) => setSelectedEvent({ ...selectedEvent, description: e.target.value })}
                />
              </div>
              {/* <div style={{ width: "100%", marginBottom: '5px' }}>
  <div style={{ width: "100%", maxHeight: "160px", overflowY: "auto" }}>
    <label style={{ display: 'block', marginBottom: '4px', fontSize: '16px', textAlign: "left", fontWeight: "700" }}>
      Attachment:
      {files.length > 0 && (
        <span style={{ fontSize: "14px", color: "#333", marginLeft: "8px" }}>
          {files.length} file{files.length > 1 ? "s" : ""} selected
        </span>
      )}
    </label>

    <input
      type="file"
      multiple
      onChange={handleFileChangeevent}
      accept=".pdf,.doc,.docx"
      style={{
        width: '100%',
        padding: "4px",
        borderRadius: "4px",
        border: "1px solid #ccc",
        paddingLeft: "10px",
        fontSize: '13px',
        cursor: "pointer"
      }}
    />

    {files.length > 0 && (
      <ul style={{
        textAlign: "left",
        margin: 0,
        listStyle: "none",
        padding: 0,
        overflowY: "auto",
        maxHeight: "100px",
      }}>
        {files.map((file, index) => (
          <li key={index} style={{
            display: "flex",
            alignItems: "center",
            gap: "8px",
            marginBottom: "4px",
          }}>
            <span>{file.name}</span>
            <button
              type="button"
              onClick={() => handleRemoveFile(index)}
              style={{
                background: "transparent",
                border: "none",
                color: "red",
                fontWeight: "bold",
                cursor: "pointer",
                fontSize: "14px",
              }}
            >
              ❌
            </button>
          </li>
        ))}
      </ul>
    )}
  </div>
</div> */}
              <div style={{ width: "100%", marginBottom: '5px' }}>

                <label style={{ display: 'block', marginBottom: '4px', fontSize: '16px', textAlign: "left", fontWeight: "700" }}>
                  Attachment:
                  {(files.length > 0 || existingFiles.length > 0) && (
                    <span style={{ fontSize: "14px", color: "#333", marginLeft: "8px" }}>
                      {existingFiles.length + files.length} file(s) selected
                    </span>
                  )}
                </label>

                <input
                  type="file"
                  multiple
                  onChange={handleFileChangeevent}
                  accept=".pdf,.doc,.docx"
                  style={{
                    width: '100%',
                    padding: "4px",
                    borderRadius: "4px",
                    border: "1px solid #ccc",
                    paddingLeft: "10px",
                    fontSize: '13px',
                    cursor: "pointer"
                  }}
                />

                <div style={{ width: "100%", maxHeight: "100px", overflowY: "auto" }}>
                  {Array.isArray(existingFiles) && existingFiles.length > 0 && (
                    <ul style={{ padding: 0, listStyle: "none", marginTop: "10px" }}>
                      {existingFiles.map((file, index) => (
                        <li
                          key={index}
                          style={{
                            display: "flex",
                            alignItems: "center",
                            gap: "8px",
                            marginBottom: "4px",
                          }}
                        >
                          <span>{file.filename}</span>
                          <button
                            type="button"
                            onClick={() => handleRemoveExistingFile(index)}
                            style={{
                              background: "transparent",
                              border: "none",
                              color: "red",
                              fontWeight: "bold",
                              cursor: "pointer",
                              fontSize: "14px",
                            }}
                          >
                            ❌
                          </button>
                        </li>
                      ))}
                    </ul>
                  )}
                  {/* New files */}
                  {files.length > 0 && (
                    <ul style={{ padding: 0, listStyle: "none", marginTop: "10px" }}>
                      {files.map((file, index) => (
                        <li key={index} style={{ display: "flex", alignItems: "center", gap: "8px", marginBottom: "4px" }}>
                          <span>{file.name}</span>
                          <button
                            type="button"
                            onClick={() => handleRemoveFile(index)}
                            style={{ background: "transparent", border: "none", color: "red", fontWeight: "bold", cursor: "pointer", fontSize: "14px" }}
                          >
                            ❌
                          </button>
                        </li>
                      ))}
                    </ul>
                  )}


                </div>
              </div>


              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                <div style={{ width: '48%' }}>
                  <label style={{ marginBottom: '4px', fontSize: '16px', textAlign: "left" }}>Start Date</label>
                  <input
                    type="date"
                    value={startDateValue}
                    style={{ width: '100%', height: "35px", borderRadius: "5px", border: "1px solid gray", paddingLeft: "10px" }}
                    onChange={handleStartDateChange}
                  />
                </div>
                <div style={{ width: '48%' }}>
                  <label style={{  marginBottom: '4px', fontSize: '16px', textAlign: "left" }}>End Date</label>
                  <input
                    type="date"
                    value={endDateValue}
                    style={{ width: '100%', height: "35px", borderRadius: "5px", border: "1px solid gray", paddingLeft: "10px" }}
                    onChange={handleEndDateChange}
                  />
                  <span>{error && <p style={{ color: 'red' }}>{error}</p>}</span>
                </div>
              </div>
              <div style={{ marginBottom: '5px' }}>
                <label style={{ marginBottom: '4px', fontSize: '16px', textAlign: "left" }}>Time Zone</label>
                <select
                  value={selectedEvent?.time_zone || ''}
                  style={{ width: '100%', height: "35px", borderRadius: "5px", border: "1px solid gray", paddingLeft: "10px" }}
                  onChange={(e) => setSelectedEvent({ ...selectedEvent, time_zone: e.target.value })}
                >
                  <option value="">Select a time zone</option>
                  <option value="Asia/Kolkata">Asia/Kolkata</option>
                  <option value="America/New_York">America/New_York</option>
                  <option value="Europe/London">Europe/London</option>
                  <option value="Australia/Sydney">Australia/Sydney</option>
                  <option value="Asia/Tokyo">Asia/Tokyo</option>
                </select>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '20px' }}>
                <div style={{ width: '100%' }}>
                  <label style={{ display: 'block', marginBottom: '4px', fontSize: '16px', textAlign: "left" }}>Start Time*</label>
                  <input
                    type="time"
                    list="startTimeOptions"
                    value={selectedEvent.start_time || ''}
                    onChange={(e) => handleStartTimeChange(e)}
                    style={{ width: '90%', height: '40px', borderRadius: '4px', border: '1px solid #ccc', paddingLeft: '10px', fontSize: '16px' }}
                  />
                  <datalist id="startTimeOptions">
                    {timeOptions.map(option => (
                      <option key={option.value} value={option.value}></option>
                    ))}
                  </datalist>
                </div>

                <div style={{ width: '100%' }}>
                  <label style={{ display: 'block', marginBottom: '4px', fontSize: '16px', textAlign: "left" }}>End Time*</label>
                  <input
                    type="time"
                    list="endTimeOptions"
                    value={selectedEvent.end_time || ''}
                    onChange={(e) => handleEndTimeChange(e)}
                    style={{ width: '100%', height: '40px', borderRadius: '4px', border: '1px solid #ccc', paddingLeft: '10px', fontSize: '16px' }}
                  />
                  <datalist id="endTimeOptions">
                    {timeOptions.map(option => (
                      <option key={option.value} value={option.value}></option>
                    ))}
                  </datalist>
                </div>
              </div>
              <div className="btns" style={{
                position: "sticky",
                bottom: "0",
                backgroundColor: "#fff",
                padding: "10px 0px",
                zIndex: "100",
                display: "flex",
                gap: "10px",
                justifyContent: "flex-end"
              }}>
                <button
                  type="button"
                  style={{ backgroundColor: "#e81123", color: "white", border: "none", borderRadius: "4px", padding: '0px 16px', fontSize: '16px', cursor: 'pointer', height: "30px", width: "80px" }}
                  onClick={EditcloseModal}
                >
                  Close
                </button>
                <button
                  type="submit"
                  style={{ backgroundColor: "#32406D", color: "white", border: "none", borderRadius: "4px", padding: '0px 16px', fontSize: '16px', cursor: 'pointer', height: "30px", width: "80px" }}
                >
                  {waitForSubmission1 ? "" : "Save"}
                  <ThreeDots
                    wrapperClass="ovalSpinner"
                    wrapperStyle={{
                      position: "absolute",
                      right: "-3px",
                      transform: "translate(-50%, -50%)",
                    }}
                    visible={waitForSubmission1}
                    height="45"
                    width="45"
                    color="white"
                    ariaLabel="oval-loading"
                  />
                </button>
              </div>
            </div>
          </form>
        </Modal>
        <Modal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          style={{
            overlay: {
              backgroundColor: 'rgba(0, 0, 0, 0.75)',
              zIndex: 9999,
            },
            content: {
              color: 'lightsteelblue',
              top: '50%',
              left: '50%',
              right: 'auto',
              bottom: 'auto',
              marginRight: '-50%',
              transform: 'translate(-50%, -50%)',
              width: "450px",
              height: "150px"
            }
          }}>
          <div style={{ textAlign: 'center', marginTop: "20px" }}>
            <p style={{ color: "#000" }}>{modalMessage}</p>
          </div>
          <div style={{ display: 'flex', justifyContent: 'center', gap: '10px', width: '100%', marginTop: "30px" }}>
            <button
              onClick={() => {
                setIsModalOpen(false);
                if (responseSuccess) {
                  setShowCalendar(true);
                }
              }}
              style={{
                color: "white",
                backgroundColor: "green",
                border: "none",
                width: "50px",
                height: "25px",
                borderRadius: "5px"
              }}
            >
              Ok
            </button>
            <button
              onClick={() => setIsModalOpen(false)}
              style={{
                color: "white",
                backgroundColor: "red",
                border: "none",
                width: "50px",
                height: "25px",
                borderRadius: "5px"
              }}
            >
              Close
            </button>
          </div>
        </Modal>
        <Modal
          isOpen={showImageModal} // Control modal visibility
          onRequestClose={closeModals} // Close modal when clicking outside
          contentLabel="Profile Image"
          style={{
            overlay: {
              backgroundColor: 'rgba(0, 0, 0, 0.7)', // Dark overlay
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center', // Ensures overlay content is centered
              zIndex: 1000, // Make sure modal appears above other content
            },
            content: {
              position: 'relative',
              backgroundColor: '#fff',
              borderRadius: '10px',
              padding: '20px',
              maxWidth: '30vw',
              maxHeight: '70vh',
              margin: 'auto',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              border: 'none',
              outline: 'none', // Removes the default outline
              zIndex: 1001, // Ensures modal content appears above overlay
            },
          }}
        >
          <div style={{ position: 'relative' }}>
            <img
              src={profileImage}
              alt="Profile"
              style={{ maxWidth: '100%', maxHeight: '80vh' }}
            />
            <button
              onClick={closeModals}
              style={{
                position: 'absolute',
                top: '10px',
                right: '10px',
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
                border: 'none',
                borderRadius: '50%',
                padding: '10px',
                cursor: 'pointer',
              }}
            >
              X
            </button>
          </div>
        </Modal>

      </div>
    </div>
  );
})


export default LeftNav;



